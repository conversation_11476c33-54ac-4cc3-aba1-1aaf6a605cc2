"""Authentication module for API endpoints with Supabase integration."""

import logging
from typing import Optional, Dict, Any
from fastapi import HTT<PERSON>Exception, status, Depends, Header
from app.core.supabase import supabase

logger = logging.getLogger(__name__)


async def verify_api_key():
    """
    Simple API key verification.
    For now, this is a placeholder that always passes.
    In production, you would implement proper API key validation.
    """
    # For development, we'll skip API key validation
    # In production, implement proper API key checking here
    return True


async def get_current_user_from_token(authorization: Optional[str] = Header(None)) -> Dict[str, Any]:
    """
    Get current user information from Supabase JWT token.

    Args:
        authorization: Authorization header with Bearer token

    Returns:
        User information dictionary

    Raises:
        HTTPException: If token is invalid or user not found
    """
    # For development, allow anonymous access
    if not authorization:
        logger.warning("No authorization header provided, using anonymous user")
        return {"user_id": "anonymous", "username": "anonymous", "email": "<EMAIL>"}

    try:
        # Extract token from "Bearer <token>" format
        if not authorization.startswith("Bearer "):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authorization header format"
            )

        token = authorization.split(" ")[1]

        # Verify token with Supabase
        user_response = supabase.auth.get_user(token)

        if not user_response.user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid or expired token"
            )

        user = user_response.user

        return {
            "user_id": user.id,
            "username": user.user_metadata.get("full_name") or user.email.split("@")[0],
            "email": user.email,
            "metadata": user.user_metadata,
            "jwt_token": token  # ✅ Include the raw JWT token for storage operations
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error verifying user token: {str(e)}")
        # For development, fall back to anonymous user
        logger.warning("Token verification failed, using anonymous user for development")
        return {"user_id": "anonymous", "username": "anonymous", "email": "<EMAIL>"}


def get_current_user():
    """
    Get current user information.
    Placeholder for user authentication.
    """
    return {"user_id": "anonymous", "username": "anonymous"}


def require_auth():
    """
    Dependency that requires authentication.
    Placeholder for authentication requirement.
    """
    return True


# Dependency for getting current user in endpoints
CurrentUser = Depends(get_current_user_from_token)
